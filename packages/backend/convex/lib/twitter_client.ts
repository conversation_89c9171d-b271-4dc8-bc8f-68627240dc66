/**
 * Tweet<PERSON> Client for BuddyChip Pro
 * 
 * This client provides a clean interface to interact with <PERSON><PERSON><PERSON> (twitterapi.io)
 * for fetching tweets, user data, mentions, and engagement metrics.
 */

// Import types and utilities from new modular structure
import type {
  Twitter<PERSON><PERSON>weet,
  TwitterUser,
  TwitterTweet,
  TwitterApiResponse,
  TwitterClientConfig,
  ConvexContext,
  UserTweetsOptions,
  SearchMentionsOptions,
  SearchTweetsOptions,
  UserTweetsResponse,
  TweetResponse,
  SearchResponse,
} from "../types/twitter";

import { TweetIOError, parseTwitterApiError, shouldRetryError } from "../errors/twitter_errors";
import { TwitterRateLimiter } from "./twitter_rate_limiting";
import { TwitterAPIMonitor } from "./twitter_monitoring";
import { 
  extractTweetIdFromUrl,
  extractUsernameFromUrl,
  convertTwitterIOTweet,
  convertTwitterIOUser,
  deduplicateUsers,
  cleanUsername 
} from "./twitter_utils";

export class TweetIOClient {
  private baseUrl: string;
  private apiKey: string;
  private config: TwitterClientConfig;
  private rateLimiter: TwitterRateLimiter;
  private monitor: TwitterAPIMonitor;

  constructor(apiKey: string, options: { 
    baseUrl?: string;
    enableMonitoring?: boolean;
    ctx?: ConvexContext;
  } = {}) {
    if (!apiKey) {
      throw new Error("TwitterAPI.io API key is required");
    }
    
    this.apiKey = apiKey;
    this.baseUrl = options.baseUrl || "https://api.twitterapi.io";
    
    // Load configuration if available
    try {
      const { getTwitterAPIConfig } = require("./config");
      this.config = getTwitterAPIConfig();
    } catch (error) {
      // Config not available, use defaults
      this.config = {
        rateLimitHandling: {
          enableRateLimiting: true,
          defaultDelay: 1000,
          retryAttempts: 3,
          retryDelay: 5000,
          exponentialBackoff: true,
        },
        scraping: {
          requestTimeout: 30000,
        },
        quotaManagement: {
          enableQuotaTracking: true,
        },
        monitoring: {
          enableUsageLogging: true,
        },
      };
    }

    // Initialize rate limiter and monitor
    this.rateLimiter = new TwitterRateLimiter(this.config);
    this.monitor = new TwitterAPIMonitor(
      this.config, 
      options.ctx, 
      options.enableMonitoring ?? true
    );
  }

  /**
   * Check if we're rate limited for a specific endpoint
   */
  private isRateLimited(endpoint: string): boolean {
    return this.rateLimiter.isRateLimited(endpoint);
  }

  /**
   * Update rate limit information from response headers
   */
  private updateRateLimitInfo(endpoint: string, headers: Headers): void {
    this.rateLimiter.updateRateLimitInfo(endpoint, headers);
  }

  /**
   * Check quota limits before making requests
   */
  private async checkQuotaLimits(): Promise<boolean> {
    return await this.monitor.checkQuotaLimits();
  }

  /**
   * Track API usage for monitoring
   */
  private async trackUsage(
    endpoint: string,
    startTime: number,
    response: Response,
    requestCount: number = 1,
    error?: Error
  ): Promise<void> {
    await this.monitor.trackUsage(endpoint, startTime, response, requestCount, error);
  }

  /**
   * Apply exponential backoff for retries
   */
  private async applyBackoff(attempt: number, baseDelay: number): Promise<void> {
    await this.rateLimiter.applyBackoff(attempt, baseDelay);
  }

  /**
   * Make authenticated request to TwitterAPI.io with comprehensive monitoring
   */
  private async makeRequest<T>(
    endpoint: string,
    params?: Record<string, string | number | boolean>
  ): Promise<TwitterApiResponse<T>> {
    // Check quota limits before making request
    await this.checkQuotaLimits();

    // Check rate limits
    if (this.isRateLimited(endpoint)) {
      const limitInfo = this.rateLimiter.getRateLimitInfo(endpoint);
      const resetTime = limitInfo ? new Date(limitInfo.reset * 1000) : new Date();
      throw new TweetIOError(
        `Rate limit exceeded for ${endpoint}. Resets at ${resetTime.toISOString()}`,
        429,
        limitInfo || undefined
      );
    }

    const url = new URL(`${this.baseUrl}${endpoint}`);
    
    // Add query parameters
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value.toString());
      });
    }

    let lastError: Error | null = null;
    const maxRetries = this.config.rateLimitHandling.retryAttempts;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const startTime = Date.now();
      
      try {
        // Apply rate limiting delay
        if (attempt > 1) {
          await this.applyBackoff(attempt, this.config.rateLimitHandling.retryDelay);
        } else if (this.config.rateLimitHandling.enableRateLimiting) {
          await this.rateLimiter.applyDefaultDelay();
        }

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.scraping.requestTimeout);

        const response = await fetch(url.toString(), {
          method: "GET",
          headers: {
            "X-API-Key": this.apiKey,
            "Content-Type": "application/json",
          },
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        // Update rate limit info
        this.updateRateLimitInfo(endpoint, response.headers);

        if (!response.ok) {
          const errorText = await response.text();
          const parsedError = parseTwitterApiError(response, errorText);

          const error = new TweetIOError(
            parsedError.message,
            response.status,
            this.rateLimiter.getRateLimitInfo(endpoint) || undefined
          );

          // Track failed request
          await this.trackUsage(endpoint, startTime, response, 1, error);

          // Use new error handling utilities
          if (!shouldRetryError(error)) {
            throw error;
          }

          // For rate limiting, update our state and potentially retry
          if (response.status === 429) {
            this.rateLimiter.updateRateLimitFromResponse(endpoint, response);
            
            if (attempt === maxRetries) {
              throw error;
            }
            lastError = error;
            continue;
          }

          // For other errors, retry if we have attempts left
          if (attempt === maxRetries) {
            throw error;
          }
          lastError = error;
          continue;
        }

        const data = await response.json();
        
        // Track successful request
        await this.trackUsage(endpoint, startTime, response, 1);

        // TwitterAPI.io returns data directly, not in a TwitterApiResponse wrapper
        return data as TwitterApiResponse<T>;

      } catch (error) {
        if (error instanceof TweetIOError) {
          lastError = error;
          if (attempt === maxRetries) {
            throw error;
          }
          continue;
        }
        
        // Handle network errors, timeouts, etc.
        const networkError = new TweetIOError(
          `Network error: ${error instanceof Error ? error.message : String(error)}`
        );
        
        if (attempt === maxRetries) {
          throw networkError;
        }
        lastError = networkError;
      }
    }

    // If we get here, all retries failed
    throw lastError || new TweetIOError("All retry attempts failed");
  }

  /**
   * Get user information by username
   */
  async getUserByUsername(username: string): Promise<TwitterUser | null> {
    try {
      const cleanedUsername = cleanUsername(username);
      const response = await this.makeRequest<TwitterUser>("/twitter/user/info", {
        userName: cleanedUsername
      });

      return response.data || null;
    } catch (error) {
      console.error(`Error fetching user ${username}:`, error);
      return null;
    }
  }

  /**
   * PHASE 2: Get user information by user ID
   * This method fills gaps when TwitterAPI.io doesn't return complete author data
   */
  async getUserById(userId: string): Promise<TwitterUser | null> {
    try {
      const response = await this.makeRequest<TwitterUser>("/twitter/user/info", {
        userId: userId
      });

      return response.data || null;
    } catch (error) {
      console.error(`Error fetching user by ID ${userId}:`, error);
      return null;
    }
  }

  /**
   * PHASE 2: Batch lookup multiple users by their IDs
   * Efficient way to fetch missing author data for multiple mentions
   */
  async getUsersByIds(userIds: string[]): Promise<TwitterUser[]> {
    if (userIds.length === 0) return [];
    
    try {
      // TwitterAPI.io may support batch requests - check if available
      // If not, we'll fall back to individual requests with rate limiting
      const users: TwitterUser[] = [];
      
      for (const userId of userIds) {
        const user = await this.getUserById(userId);
        if (user) {
          users.push(user);
        }
        
        // Small delay between requests to respect rate limits
        if (userIds.indexOf(userId) < userIds.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
      
      return users;
    } catch (error) {
      console.error(`Error fetching users by IDs:`, error);
      return [];
    }
  }

  /**
   * Get recent tweets from a user
   */
  async getUserTweets(
    userId: string,
    options: UserTweetsOptions = {}
  ): Promise<{ tweets: TwitterTweet[]; users: TwitterUser[] }> {
    try {
      const params: Record<string, string | number | boolean> = {
        "tweet.fields": "id,text,author_id,created_at,conversation_id,in_reply_to_user_id,referenced_tweets,public_metrics,attachments,context_annotations,entities",
        "user.fields": "id,name,username,profile_image_url,verified,public_metrics",
        "expansions": "author_id,referenced_tweets.id,referenced_tweets.id.author_id"
      };

      if (options.maxResults) params.max_results = Math.min(options.maxResults, 100);
      if (options.sinceId) params.since_id = options.sinceId;
      if (options.untilId) params.until_id = options.untilId;
      if (options.excludeReplies) params.exclude = "replies";
      if (options.excludeRetweets) params.exclude = options.excludeReplies ? "replies,retweets" : "retweets";

      const response = await this.makeRequest<TwitterTweet[]>(`/v2/users/${userId}/tweets`, params);

      return {
        tweets: response.data || [],
        users: response.includes?.users || []
      };
    } catch (error) {
      console.error(`Error fetching tweets for user ${userId}:`, error);
      return { tweets: [], users: [] };
    }
  }

  /**
   * Get tweets by username (convenience method)
   */
  async getTweetsByUsername(
    username: string,
    options: UserTweetsOptions = {}
  ): Promise<UserTweetsResponse> {
    const user = await this.getUserByUsername(username);
    if (!user) {
      return { tweets: [], users: [], targetUser: null };
    }

    const result = await this.getUserTweets(user.id, options);
    return {
      ...result,
      targetUser: user
    };
  }

  /**
   * Get a specific tweet by ID using correct TwitterAPI.io endpoint
   */
  async getTweetById(tweetId: string): Promise<TweetResponse> {
    try {
      // Use TwitterAPI.io's correct endpoint: /twitter/tweets with tweet_ids parameter
      const response = await this.makeRequest<any>(`/twitter/tweets`, {
        tweet_ids: tweetId, // Correct parameter name according to docs
      });

      // TwitterAPI.io returns tweets in this format: { tweets: [...], users: [...], status: "success" }
      const tweets = response.tweets || response.data || [];
      const tweet = tweets[0] || null;
      
      // Handle users array from response
      let users = response.users || [];
      
      // If no users in response but we have tweet with author data, construct user object
      if (users.length === 0 && tweet && tweet.author) {
        users.push({
          id: tweet.author.id || tweet.author_id,
          name: tweet.author.name || tweet.author.displayname,
          username: tweet.author.username || tweet.author.handle,
          profile_image_url: tweet.author.avatar || tweet.author.profile_image_url,
          verified: tweet.author.verified || false,
          followers_count: tweet.author.followers || tweet.author.followers_count || 0,
          public_metrics: {
            followers_count: tweet.author.followers || tweet.author.followers_count || 0,
            following_count: tweet.author.following || tweet.author.following_count || 0,
            tweet_count: tweet.author.statuses || tweet.author.tweet_count || 0,
          }
        });
      }
      
      // Fix missing username in users array using fallback resolution
      users = users.map((user: any) => {
        if (!user.username && user.name) {
          // Generate fallback username from display name
          const cleanName = user.name
            .toLowerCase()
            .replace(/[^a-z0-9]/g, '')
            .substring(0, 15);
          
          if (cleanName.length >= 3) {
            user.username = `${cleanName}_${user.id.substring(0, 4)}`;
          } else {
            user.username = `user_${user.id.substring(0, 8)}`;
          }
        }
        return user;
      });

      return {
        tweet: tweet ? {
          id: tweet.id || tweet.tweet_id,
          text: tweet.text || tweet.full_text || tweet.content,
          author_id: tweet.author?.id || tweet.author_id,
          created_at: tweet.created_at || tweet.date,
          conversation_id: tweet.conversation_id || tweet.id,
          in_reply_to_user_id: tweet.in_reply_to_user_id,
          referenced_tweets: tweet.referenced_tweets || [],
          public_metrics: {
            like_count: tweet.likes || tweet.favorite_count || 0,
            retweet_count: tweet.retweets || tweet.retweet_count || 0,
            reply_count: tweet.replies || tweet.reply_count || 0,
            impression_count: tweet.views || tweet.impression_count || 0,
          },
          attachments: tweet.attachments,
          entities: tweet.entities,
        } : null,
        users
      };
    } catch (error) {
      console.error(`Error fetching tweet ${tweetId}:`, error);
      return { tweet: null, users: [] };
    }
  }

  /**
   * Get a tweet from a Twitter URL
   */
  async getTweetFromUrl(url: string): Promise<TweetResponse> {
    const tweetId = extractTweetIdFromUrl(url);
    if (!tweetId) {
      throw new TweetIOError("Invalid Twitter URL: Could not extract tweet ID");
    }

    // Extract username from URL as well since TwitterAPI.io doesn't always provide it
    const usernameFromUrl = extractUsernameFromUrl(url);

    const result = await this.getTweetById(tweetId);
    
    // If we have the username from URL, use it for the tweet author
    if (usernameFromUrl && result.users.length > 0) {
      result.users = result.users.map(user => {
        // Always use URL username if available and user is the tweet author
        if (user.id === result.tweet?.author_id) {
          user.username = usernameFromUrl;
        }
        return user;
      });
    }

    return result;
  }

  /**
   * Search for mentions of a username using TwitterAPI.io user mentions endpoint
   */
  async searchMentions(
    username: string,
    options: SearchMentionsOptions = {}
  ): Promise<{ tweets: TwitterTweet[]; users: TwitterUser[] }> {
    try {
      const cleanedUsername = cleanUsername(username);

      const params: Record<string, string | number | boolean> = {
        userName: cleanedUsername, // TwitterAPI.io expects userName parameter
      };

      // Add optional time parameters if provided (TwitterAPI.io expects Unix timestamps)
      if (options.startTime) {
        const timestamp = typeof options.startTime === 'string' 
          ? Math.floor(new Date(options.startTime).getTime() / 1000)
          : Math.floor(options.startTime / 1000);
        params.sinceTime = timestamp;
      }
      
      if (options.endTime) {
        const timestamp = typeof options.endTime === 'string'
          ? Math.floor(new Date(options.endTime).getTime() / 1000)
          : Math.floor(options.endTime / 1000);
        params.untilTime = timestamp;
      }

      // TwitterAPI.io returns exactly 20 mentions per page, use cursor for pagination
      if (options.cursor) {
        params.cursor = options.cursor;
      }

      // Optimize for maxResults by using pagination if needed
      if (options.maxResults && options.maxResults > 20) {
        // Will need to implement pagination in calling code since TwitterAPI.io returns max 20 per page
      }

      // Use TwitterAPI.io's dedicated user mentions endpoint
      const response = await this.makeRequest<{ 
        tweets: TwitterIOTweet[]; 
        has_next_page: boolean; 
        next_cursor: string;
        status: string;
        msg?: string;
      }>("/twitter/user/mentions", params);

      // TwitterAPI.io returns mentions directly in the response
      // Response format: { tweets: [...], has_next_page: boolean, next_cursor: string, status: "success" }
      let allTweets: TwitterIOTweet[] = [];
      let currentCursor = options.cursor || "";
      let hasNextPage = true;
      let pageCount = 0;
      const maxPages = Math.ceil((options.maxResults || 20) / 20); // 20 mentions per page

      // Handle pagination if maxResults > 20
      while (hasNextPage && pageCount < maxPages) {
        let pageResponse;
        
        if (pageCount === 0) {
          // Use the response we already have for the first page
          pageResponse = response;
        } else {
          // Make additional requests for subsequent pages
          const pageParams = { ...params, cursor: currentCursor };
          pageResponse = await this.makeRequest<{ 
            tweets: TwitterIOTweet[]; 
            has_next_page: boolean; 
            next_cursor: string;
            status: string;
            msg?: string;
          }>("/twitter/user/mentions", pageParams);
        }

        // Extract tweets from response - TwitterAPI.io may return directly or wrapped in data
        const responseData = (pageResponse as any);
        const pageTweets = responseData.tweets || responseData.data?.tweets || [];
        allTweets.push(...pageTweets);

        // Check pagination
        hasNextPage = responseData.has_next_page || responseData.data?.has_next_page || false;
        currentCursor = responseData.next_cursor || responseData.data?.next_cursor || "";
        pageCount++;

        // Respect rate limits between pages
        if (hasNextPage && pageCount < maxPages) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // Limit results if maxResults specified
      if (options.maxResults && allTweets.length > options.maxResults) {
        allTweets = allTweets.slice(0, options.maxResults);
      }

      // Convert TwitterIO format to our expected format using utility functions
      const tweets: TwitterTweet[] = allTweets.map(convertTwitterIOTweet);

      // Extract users from tweets using utility functions  
      const users: TwitterUser[] = deduplicateUsers(
        allTweets
          .map((tweet: any) => convertTwitterIOUser(tweet.author))
          .filter((user: any): user is TwitterUser => user !== null)
      );

      return {
        tweets,
        users
      };
    } catch (error) {
      console.error(`❌ Error searching mentions for ${username}:`, error);
      return { tweets: [], users: [] };
    }
  }

  /**
   * Search for tweets with a custom query using TwitterAPI.io
   */
  async searchTweets(
    query: string,
    options: SearchTweetsOptions = {}
  ): Promise<SearchResponse> {
    try {
      const params: Record<string, string | number | boolean> = {
        query,
        queryType: options.queryType || "Latest"
      };

      if (options.cursor) params.cursor = options.cursor;

      const response = await this.makeRequest<{ tweets: TwitterIOTweet[]; has_next_page: boolean; next_cursor: string }>("/twitter/tweet/advanced_search", params);

      // Convert TweetIO format to our expected format using utility functions
      const tweets: TwitterTweet[] = (response.data?.tweets || []).map(convertTwitterIOTweet);

      // Extract users from tweet authors using utility functions
      const users: TwitterUser[] = deduplicateUsers(
        (response.data?.tweets || [])
          .map(tweet => convertTwitterIOUser(tweet.author))
          .filter((user): user is TwitterUser => user !== null)
      );

      return {
        tweets,
        users,
        hasNextPage: response.data?.has_next_page || false,
        nextCursor: response.data?.next_cursor || ""
      };
    } catch (error) {
      console.error(`Error searching tweets with query "${query}":`, error);
      return { tweets: [], users: [] };
    }
  }

  /**
   * Get rate limit status for debugging
   */
  getRateLimitStatus() {
    return this.rateLimiter.getAllRateLimitStatus();
  }
}

/**
 * Create a TwitterAPI.io client instance
 * For use in Convex functions - pass the API key directly from context
 */
export function createTweetIOClient(apiKey?: string, ctx?: ConvexContext): TweetIOClient {
  const key = apiKey || process.env.TWITTERAPI_IO_API_KEY || process.env.TWEETIO_API_KEY;
  if (!key) {
    throw new Error(
      "TWITTERAPI_IO_API_KEY environment variable is required. " +
      "Please add it to your .env.local file or set it in your Convex environment variables. " +
      "Get your API key from https://twitterapi.io/ and see setup instructions in CLAUDE.md"
    );
  }

  let config;
  try {
    const { getTwitterAPIConfig } = require("./config");
    config = getTwitterAPIConfig();
  } catch (error) {
    // Use default base URL if config not available
    config = { baseUrl: "https://api.twitterapi.io" };
  }

  return new TweetIOClient(key, {
    baseUrl: config.baseUrl,
    enableMonitoring: true,
    ctx,
  });
}

/**
 * Legacy function name for backwards compatibility
 * @deprecated Use createTweetIOClient instead
 */
export function createTwitterClient(apiKey?: string): TweetIOClient {
  return createTweetIOClient(apiKey);
}

// Re-export utility functions for backwards compatibility
export { 
  normalizeTwitterTweet, 
  calculateMentionPriority 
} from "./twitter_utils";