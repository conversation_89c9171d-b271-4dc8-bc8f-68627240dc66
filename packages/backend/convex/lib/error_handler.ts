/**
 * Production Error Handler
 * 🔐 SECURITY: Sanitizes error messages and prevents information disclosure
 */

import { logger } from './secure_logger';
import { isProduction, isDevelopment } from './production_config';

/**
 * Standard error types with safe production messages
 */
export enum ErrorType {
  AUTHENTICATION_REQUIRED = 'AUTHENTICATION_REQUIRED',
  AUTHORIZATION_FAILED = 'AUTHORIZATION_FAILED',
  VALIDATION_FAILED = 'VALIDATION_FAILED',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  EXTERNAL_API_ERROR = 'EXTERNAL_API_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  BAD_REQUEST = 'BAD_REQUEST',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
}

/**
 * Error information for each error type
 */
const ERROR_INFO = {
  [ErrorType.AUTHENTICATION_REQUIRED]: {
    statusCode: 401,
    productionMessage: 'Authentication required. Please sign in.',
    logLevel: 'warn' as const,
  },
  [ErrorType.AUTHORIZATION_FAILED]: {
    statusCode: 403,
    productionMessage: 'Access denied. You do not have permission to perform this action.',
    logLevel: 'warn' as const,
  },
  [ErrorType.VALIDATION_FAILED]: {
    statusCode: 400,
    productionMessage: 'Invalid input. Please check your request and try again.',
    logLevel: 'warn' as const,
  },
  [ErrorType.RATE_LIMIT_EXCEEDED]: {
    statusCode: 429,
    productionMessage: 'Too many requests. Please try again later.',
    logLevel: 'warn' as const,
  },
  [ErrorType.RESOURCE_NOT_FOUND]: {
    statusCode: 404,
    productionMessage: 'The requested resource was not found.',
    logLevel: 'info' as const,
  },
  [ErrorType.EXTERNAL_API_ERROR]: {
    statusCode: 503,
    productionMessage: 'External service is temporarily unavailable. Please try again later.',
    logLevel: 'error' as const,
  },
  [ErrorType.DATABASE_ERROR]: {
    statusCode: 500,
    productionMessage: 'A database error occurred. Please try again later.',
    logLevel: 'error' as const,
  },
  [ErrorType.INTERNAL_ERROR]: {
    statusCode: 500,
    productionMessage: 'An internal error occurred. Please try again later.',
    logLevel: 'error' as const,
  },
  [ErrorType.BAD_REQUEST]: {
    statusCode: 400,
    productionMessage: 'Invalid request. Please check your input and try again.',
    logLevel: 'warn' as const,
  },
  [ErrorType.QUOTA_EXCEEDED]: {
    statusCode: 402,
    productionMessage: 'Usage quota exceeded. Please upgrade your plan or try again later.',
    logLevel: 'warn' as const,
  },
};

/**
 * Custom application error class
 */
export class ApplicationError extends Error {
  public readonly type: ErrorType;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly context?: Record<string, any>;

  constructor(
    type: ErrorType,
    message?: string,
    context?: Record<string, any>
  ) {
    const errorInfo = ERROR_INFO[type];
    super(message || errorInfo.productionMessage);
    
    this.type = type;
    this.statusCode = errorInfo.statusCode;
    this.isOperational = true;
    this.context = context;
    
    // Maintain proper stack trace
    Error.captureStackTrace(this, ApplicationError);
  }
}

/**
 * Sanitizes error message for production
 */
export function sanitizeErrorMessage(error: any, fallbackType: ErrorType = ErrorType.INTERNAL_ERROR): string {
  // If it's our custom error, use the type-specific message
  if (error instanceof ApplicationError) {
    const errorInfo = ERROR_INFO[error.type];
    return isProduction() ? errorInfo.productionMessage : error.message;
  }
  
  // Handle common error patterns
  if (error?.message) {
    const message = error.message.toLowerCase();
    
    // Authentication errors
    if (message.includes('authentication') || message.includes('unauthorized')) {
      return isProduction() 
        ? ERROR_INFO[ErrorType.AUTHENTICATION_REQUIRED].productionMessage
        : error.message;
    }
    
    // Authorization errors
    if (message.includes('permission') || message.includes('access denied') || message.includes('forbidden')) {
      return isProduction() 
        ? ERROR_INFO[ErrorType.AUTHORIZATION_FAILED].productionMessage
        : error.message;
    }
    
    // Validation errors
    if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
      return isProduction() 
        ? ERROR_INFO[ErrorType.VALIDATION_FAILED].productionMessage
        : error.message;
    }
    
    // Rate limiting errors
    if (message.includes('rate limit') || message.includes('too many')) {
      return isProduction() 
        ? ERROR_INFO[ErrorType.RATE_LIMIT_EXCEEDED].productionMessage
        : error.message;
    }
    
    // Not found errors
    if (message.includes('not found') || message.includes('does not exist')) {
      return isProduction() 
        ? ERROR_INFO[ErrorType.RESOURCE_NOT_FOUND].productionMessage
        : error.message;
    }
  }
  
  // For any unhandled error, use the fallback
  const errorInfo = ERROR_INFO[fallbackType];
  return isProduction() ? errorInfo.productionMessage : (error?.message || 'Unknown error');
}

/**
 * Enhanced error handler for Convex functions
 */
export function handleError(
  error: any,
  operation: string,
  context?: Record<string, any>
): never {
  const errorId = generateErrorId();
  
  // Determine error type
  let errorType: ErrorType;
  if (error instanceof ApplicationError) {
    errorType = error.type;
  } else {
    errorType = classifyError(error);
  }
  
  const errorInfo = ERROR_INFO[errorType];
  const sanitizedMessage = sanitizeErrorMessage(error, errorType);
  
  // Log error with appropriate level
  const logContext = {
    errorId,
    operation,
    errorType,
    statusCode: errorInfo.statusCode,
    ...context,
    ...(isDevelopment() && { originalMessage: error?.message }),
  };
  
  switch (errorInfo.logLevel) {
    case 'error':
      logger.error('Operation failed', error, logContext);
      break;
    case 'warn':
      logger.warn('Operation warning', logContext);
      break;
    case 'info':
      logger.info('Operation info', logContext);
      break;
  }
  
  // Create sanitized error to throw
  const productionError = new ApplicationError(errorType, sanitizedMessage, {
    errorId,
    statusCode: errorInfo.statusCode,
  });
  
  throw productionError;
}

/**
 * Classifies unknown errors into appropriate types
 */
function classifyError(error: any): ErrorType {
  if (!error) return ErrorType.INTERNAL_ERROR;
  
  const message = (error.message || '').toLowerCase();
  
  // Network/API errors
  if (message.includes('fetch') || message.includes('network') || message.includes('timeout')) {
    return ErrorType.EXTERNAL_API_ERROR;
  }
  
  // Database errors
  if (message.includes('database') || message.includes('query') || message.includes('transaction')) {
    return ErrorType.DATABASE_ERROR;
  }
  
  // Validation errors
  if (message.includes('invalid') || message.includes('required') || message.includes('format')) {
    return ErrorType.VALIDATION_FAILED;
  }
  
  // Default to internal error
  return ErrorType.INTERNAL_ERROR;
}

/**
 * Generates a unique error ID for tracking
 */
function generateErrorId(): string {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Wrapper for Convex functions with error handling
 */
export function withErrorHandling<T extends any[], R>(
  operation: string,
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    try {
      return await handler(...args);
    } catch (error) {
      handleError(error, operation);
    }
  };
}

/**
 * Creates specific error types for common scenarios
 */
export const createError = {
  authenticationRequired: (message?: string) => 
    new ApplicationError(ErrorType.AUTHENTICATION_REQUIRED, message),
    
  authorizationFailed: (message?: string) => 
    new ApplicationError(ErrorType.AUTHORIZATION_FAILED, message),
    
  validationFailed: (message?: string, context?: Record<string, any>) => 
    new ApplicationError(ErrorType.VALIDATION_FAILED, message, context),
    
  rateLimitExceeded: (message?: string) => 
    new ApplicationError(ErrorType.RATE_LIMIT_EXCEEDED, message),
    
  resourceNotFound: (resource: string) => 
    new ApplicationError(ErrorType.RESOURCE_NOT_FOUND, `${resource} not found`),
    
  externalApiError: (service: string, originalError?: any) => 
    new ApplicationError(ErrorType.EXTERNAL_API_ERROR, `${service} API error`, { originalError }),
    
  databaseError: (operation: string, originalError?: any) => 
    new ApplicationError(ErrorType.DATABASE_ERROR, `Database ${operation} failed`, { originalError }),
    
  quotaExceeded: (quotaType: string) => 
    new ApplicationError(ErrorType.QUOTA_EXCEEDED, `${quotaType} quota exceeded`),
};

/**
 * Validates that an error is safe to return to client
 */
export function isSafeError(error: any): boolean {
  return error instanceof ApplicationError && error.isOperational;
}