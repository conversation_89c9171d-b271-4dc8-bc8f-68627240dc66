/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type * as admin_cacheManagement from "../admin/cacheManagement.js";
import type * as admin_dataManagement from "../admin/dataManagement.js";
import type * as ai_ensembleOrchestrator from "../ai/ensembleOrchestrator.js";
import type * as ai_sentimentAnalysis from "../ai/sentimentAnalysis.js";
import type * as ai_testNewModels from "../ai/testNewModels.js";
import type * as ai_tweetAnalysis from "../ai/tweetAnalysis.js";
import type * as ai_unifiedImageGeneration from "../ai/unifiedImageGeneration.js";
import type * as ai_viralDetection from "../ai/viralDetection.js";
import type * as ai_xaiLiveSearch from "../ai/xaiLiveSearch.js";
import type * as aiAgent from "../aiAgent.js";
import type * as aiAgentEnhanced from "../aiAgentEnhanced.js";
import type * as aiWorkflowOrchestrator from "../aiWorkflowOrchestrator.js";
import type * as analytics from "../analytics.js";
import type * as auth_walletDetection from "../auth/walletDetection.js";
import type * as billing_accessControl from "../billing/accessControl.js";
import type * as billing_subscriptions from "../billing/subscriptions.js";
import type * as billing_usage from "../billing/usage.js";
import type * as chatbot from "../chatbot.js";
import type * as credits from "../credits.js";
import type * as crons from "../crons.js";
import type * as crons_original from "../crons_original.js";
import type * as dashboard_dashboardQueries from "../dashboard/dashboardQueries.js";
import type * as debug_authDebugging from "../debug/authDebugging.js";
import type * as debug from "../debug.js";
import type * as embeddings_embeddingGeneration from "../embeddings/embeddingGeneration.js";
import type * as embeddings_embeddingMutations from "../embeddings/embeddingMutations.js";
import type * as embeddings_embeddingQueries from "../embeddings/embeddingQueries.js";
import type * as errors_twitter_errors from "../errors/twitter_errors.js";
import type * as helperMutations from "../helperMutations.js";
import type * as helpers_searchAnalytics from "../helpers/searchAnalytics.js";
import type * as imageGeneration from "../imageGeneration.js";
import type * as lib_advancedCaching from "../lib/advancedCaching.js";
import type * as lib_aiResponseCache from "../lib/aiResponseCache.js";
import type * as lib_ai_fallback_client from "../lib/ai_fallback_client.js";
import type * as lib_analysisUtils from "../lib/analysisUtils.js";
import type * as lib_bandwidthMonitor from "../lib/bandwidthMonitor.js";
import type * as lib_config from "../lib/config.js";
import type * as lib_config_validator from "../lib/config_validator.js";
import type * as lib_debugConfig from "../lib/debugConfig.js";
import type * as lib_embeddingUtils from "../lib/embeddingUtils.js";
import type * as lib_error_handler from "../lib/error_handler.js";
import type * as lib_fal_client from "../lib/fal_client.js";
import type * as lib_input_sanitizer from "../lib/input_sanitizer.js";
import type * as lib_intelligentBatching from "../lib/intelligentBatching.js";
import type * as lib_mentionCache from "../lib/mentionCache.js";
import type * as lib_model_selector from "../lib/model_selector.js";
import type * as lib_openai_client from "../lib/openai_client.js";
import type * as lib_openrouter_client from "../lib/openrouter_client.js";
import type * as lib_optimizationConfig from "../lib/optimizationConfig.js";
import type * as lib_production_config from "../lib/production_config.js";
import type * as lib_projections from "../lib/projections.js";
import type * as lib_prompt_templates from "../lib/prompt_templates.js";
import type * as lib_queryLimitEnforcer from "../lib/queryLimitEnforcer.js";
import type * as lib_rate_limiter from "../lib/rate_limiter.js";
import type * as lib_secure_logger from "../lib/secure_logger.js";
import type * as lib_security_headers from "../lib/security_headers.js";
import type * as lib_twitter_api_monitor from "../lib/twitter_api_monitor.js";
import type * as lib_twitter_client from "../lib/twitter_client.js";
import type * as lib_twitter_health_check from "../lib/twitter_health_check.js";
import type * as lib_twitter_monitoring from "../lib/twitter_monitoring.js";
import type * as lib_twitter_rate_limiting from "../lib/twitter_rate_limiting.js";
import type * as lib_twitter_utils from "../lib/twitter_utils.js";
import type * as lib_unified_image_client from "../lib/unified_image_client.js";
import type * as lib_xai_client from "../lib/xai_client.js";
import type * as mentions_mentionMutations from "../mentions/mentionMutations.js";
import type * as mentions_mentionQueries from "../mentions/mentionQueries.js";
import type * as mentions_optimizedMentionQueries from "../mentions/optimizedMentionQueries.js";
import type * as mentions_sentimentAnalytics from "../mentions/sentimentAnalytics.js";
import type * as mentions_sentimentMutations from "../mentions/sentimentMutations.js";
import type * as mentions_sentimentQueries from "../mentions/sentimentQueries.js";
import type * as monitoring_healthCheck from "../monitoring/healthCheck.js";
import type * as monitoring_smartHealthCheck from "../monitoring/smartHealthCheck.js";
import type * as queries_optimizedQueries from "../queries/optimizedQueries.js";
import type * as responseGeneration from "../responseGeneration.js";
import type * as responseMutations from "../responseMutations.js";
import type * as responseQueries from "../responseQueries.js";
import type * as storage_imageStorage from "../storage/imageStorage.js";
import type * as todos from "../todos.js";
import type * as tweets from "../tweets.js";
import type * as twitter_fetchTweetFromUrl from "../twitter/fetchTweetFromUrl.js";
import type * as twitterAccounts from "../twitterAccounts.js";
import type * as twitterScraper from "../twitterScraper.js";
import type * as types_auth from "../types/auth.js";
import type * as types_convex from "../types/convex.js";
import type * as types_optimized from "../types/optimized.js";
import type * as types_twitter from "../types/twitter.js";
import type * as userQueries from "../userQueries.js";
import type * as users from "../users.js";
import type * as wallet_verification from "../wallet/verification.js";
import type * as walletMutations from "../walletMutations.js";
import type * as workflows_automatedWorkflows from "../workflows/automatedWorkflows.js";
import type * as workflows_automatedWorkflows_broken from "../workflows/automatedWorkflows_broken.js";
import type * as workflows_smartBatchWorkflows from "../workflows/smartBatchWorkflows.js";
import type * as xaiLiveSearch from "../xaiLiveSearch.js";

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  "admin/cacheManagement": typeof admin_cacheManagement;
  "admin/dataManagement": typeof admin_dataManagement;
  "ai/ensembleOrchestrator": typeof ai_ensembleOrchestrator;
  "ai/sentimentAnalysis": typeof ai_sentimentAnalysis;
  "ai/testNewModels": typeof ai_testNewModels;
  "ai/tweetAnalysis": typeof ai_tweetAnalysis;
  "ai/unifiedImageGeneration": typeof ai_unifiedImageGeneration;
  "ai/viralDetection": typeof ai_viralDetection;
  "ai/xaiLiveSearch": typeof ai_xaiLiveSearch;
  aiAgent: typeof aiAgent;
  aiAgentEnhanced: typeof aiAgentEnhanced;
  aiWorkflowOrchestrator: typeof aiWorkflowOrchestrator;
  analytics: typeof analytics;
  "auth/walletDetection": typeof auth_walletDetection;
  "billing/accessControl": typeof billing_accessControl;
  "billing/subscriptions": typeof billing_subscriptions;
  "billing/usage": typeof billing_usage;
  chatbot: typeof chatbot;
  credits: typeof credits;
  crons: typeof crons;
  crons_original: typeof crons_original;
  "dashboard/dashboardQueries": typeof dashboard_dashboardQueries;
  "debug/authDebugging": typeof debug_authDebugging;
  debug: typeof debug;
  "embeddings/embeddingGeneration": typeof embeddings_embeddingGeneration;
  "embeddings/embeddingMutations": typeof embeddings_embeddingMutations;
  "embeddings/embeddingQueries": typeof embeddings_embeddingQueries;
  "errors/twitter_errors": typeof errors_twitter_errors;
  helperMutations: typeof helperMutations;
  "helpers/searchAnalytics": typeof helpers_searchAnalytics;
  imageGeneration: typeof imageGeneration;
  "lib/advancedCaching": typeof lib_advancedCaching;
  "lib/aiResponseCache": typeof lib_aiResponseCache;
  "lib/ai_fallback_client": typeof lib_ai_fallback_client;
  "lib/analysisUtils": typeof lib_analysisUtils;
  "lib/bandwidthMonitor": typeof lib_bandwidthMonitor;
  "lib/config": typeof lib_config;
  "lib/config_validator": typeof lib_config_validator;
  "lib/debugConfig": typeof lib_debugConfig;
  "lib/embeddingUtils": typeof lib_embeddingUtils;
  "lib/error_handler": typeof lib_error_handler;
  "lib/fal_client": typeof lib_fal_client;
  "lib/input_sanitizer": typeof lib_input_sanitizer;
  "lib/intelligentBatching": typeof lib_intelligentBatching;
  "lib/mentionCache": typeof lib_mentionCache;
  "lib/model_selector": typeof lib_model_selector;
  "lib/openai_client": typeof lib_openai_client;
  "lib/openrouter_client": typeof lib_openrouter_client;
  "lib/optimizationConfig": typeof lib_optimizationConfig;
  "lib/production_config": typeof lib_production_config;
  "lib/projections": typeof lib_projections;
  "lib/prompt_templates": typeof lib_prompt_templates;
  "lib/queryLimitEnforcer": typeof lib_queryLimitEnforcer;
  "lib/rate_limiter": typeof lib_rate_limiter;
  "lib/secure_logger": typeof lib_secure_logger;
  "lib/security_headers": typeof lib_security_headers;
  "lib/twitter_api_monitor": typeof lib_twitter_api_monitor;
  "lib/twitter_client": typeof lib_twitter_client;
  "lib/twitter_health_check": typeof lib_twitter_health_check;
  "lib/twitter_monitoring": typeof lib_twitter_monitoring;
  "lib/twitter_rate_limiting": typeof lib_twitter_rate_limiting;
  "lib/twitter_utils": typeof lib_twitter_utils;
  "lib/unified_image_client": typeof lib_unified_image_client;
  "lib/xai_client": typeof lib_xai_client;
  "mentions/mentionMutations": typeof mentions_mentionMutations;
  "mentions/mentionQueries": typeof mentions_mentionQueries;
  "mentions/optimizedMentionQueries": typeof mentions_optimizedMentionQueries;
  "mentions/sentimentAnalytics": typeof mentions_sentimentAnalytics;
  "mentions/sentimentMutations": typeof mentions_sentimentMutations;
  "mentions/sentimentQueries": typeof mentions_sentimentQueries;
  "monitoring/healthCheck": typeof monitoring_healthCheck;
  "monitoring/smartHealthCheck": typeof monitoring_smartHealthCheck;
  "queries/optimizedQueries": typeof queries_optimizedQueries;
  responseGeneration: typeof responseGeneration;
  responseMutations: typeof responseMutations;
  responseQueries: typeof responseQueries;
  "storage/imageStorage": typeof storage_imageStorage;
  todos: typeof todos;
  tweets: typeof tweets;
  "twitter/fetchTweetFromUrl": typeof twitter_fetchTweetFromUrl;
  twitterAccounts: typeof twitterAccounts;
  twitterScraper: typeof twitterScraper;
  "types/auth": typeof types_auth;
  "types/convex": typeof types_convex;
  "types/optimized": typeof types_optimized;
  "types/twitter": typeof types_twitter;
  userQueries: typeof userQueries;
  users: typeof users;
  "wallet/verification": typeof wallet_verification;
  walletMutations: typeof walletMutations;
  "workflows/automatedWorkflows": typeof workflows_automatedWorkflows;
  "workflows/automatedWorkflows_broken": typeof workflows_automatedWorkflows_broken;
  "workflows/smartBatchWorkflows": typeof workflows_smartBatchWorkflows;
  xaiLiveSearch: typeof xaiLiveSearch;
}>;
declare const fullApiWithMounts: typeof fullApi;

export declare const api: FilterApi<
  typeof fullApiWithMounts,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApiWithMounts,
  FunctionReference<any, "internal">
>;

export declare const components: {};
