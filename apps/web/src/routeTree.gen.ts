/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as TweetAssistantImport } from './routes/tweet-assistant'
import { Route as SignUpImport } from './routes/sign-up'
import { Route as SignInImport } from './routes/sign-in'
import { Route as PricingImport } from './routes/pricing'
import { Route as OnboardingImport } from './routes/onboarding'
import { Route as MentionsImport } from './routes/mentions'
import { Route as LiveSearchImport } from './routes/live-search'
import { Route as ImageGenerationImport } from './routes/image-generation'
import { Route as DashboardImport } from './routes/dashboard'
import { Route as R404Import } from './routes/404'
import { Route as SplatImport } from './routes/$splat'
import { Route as IndexImport } from './routes/index'

// Create/Update Routes

const TweetAssistantRoute = TweetAssistantImport.update({
  id: '/tweet-assistant',
  path: '/tweet-assistant',
  getParentRoute: () => rootRoute,
} as any)

const SignUpRoute = SignUpImport.update({
  id: '/sign-up',
  path: '/sign-up',
  getParentRoute: () => rootRoute,
} as any)

const SignInRoute = SignInImport.update({
  id: '/sign-in',
  path: '/sign-in',
  getParentRoute: () => rootRoute,
} as any)

const PricingRoute = PricingImport.update({
  id: '/pricing',
  path: '/pricing',
  getParentRoute: () => rootRoute,
} as any)

const OnboardingRoute = OnboardingImport.update({
  id: '/onboarding',
  path: '/onboarding',
  getParentRoute: () => rootRoute,
} as any)

const MentionsRoute = MentionsImport.update({
  id: '/mentions',
  path: '/mentions',
  getParentRoute: () => rootRoute,
} as any)

const LiveSearchRoute = LiveSearchImport.update({
  id: '/live-search',
  path: '/live-search',
  getParentRoute: () => rootRoute,
} as any)

const ImageGenerationRoute = ImageGenerationImport.update({
  id: '/image-generation',
  path: '/image-generation',
  getParentRoute: () => rootRoute,
} as any)

const DashboardRoute = DashboardImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRoute,
} as any)

const R404Route = R404Import.update({
  id: '/404',
  path: '/404',
  getParentRoute: () => rootRoute,
} as any)

const SplatRoute = SplatImport.update({
  id: '/$splat',
  path: '/$splat',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/$splat': {
      id: '/$splat'
      path: '/$splat'
      fullPath: '/$splat'
      preLoaderRoute: typeof SplatImport
      parentRoute: typeof rootRoute
    }
    '/404': {
      id: '/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof R404Import
      parentRoute: typeof rootRoute
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardImport
      parentRoute: typeof rootRoute
    }
    '/image-generation': {
      id: '/image-generation'
      path: '/image-generation'
      fullPath: '/image-generation'
      preLoaderRoute: typeof ImageGenerationImport
      parentRoute: typeof rootRoute
    }
    '/live-search': {
      id: '/live-search'
      path: '/live-search'
      fullPath: '/live-search'
      preLoaderRoute: typeof LiveSearchImport
      parentRoute: typeof rootRoute
    }
    '/mentions': {
      id: '/mentions'
      path: '/mentions'
      fullPath: '/mentions'
      preLoaderRoute: typeof MentionsImport
      parentRoute: typeof rootRoute
    }
    '/onboarding': {
      id: '/onboarding'
      path: '/onboarding'
      fullPath: '/onboarding'
      preLoaderRoute: typeof OnboardingImport
      parentRoute: typeof rootRoute
    }
    '/pricing': {
      id: '/pricing'
      path: '/pricing'
      fullPath: '/pricing'
      preLoaderRoute: typeof PricingImport
      parentRoute: typeof rootRoute
    }
    '/sign-in': {
      id: '/sign-in'
      path: '/sign-in'
      fullPath: '/sign-in'
      preLoaderRoute: typeof SignInImport
      parentRoute: typeof rootRoute
    }
    '/sign-up': {
      id: '/sign-up'
      path: '/sign-up'
      fullPath: '/sign-up'
      preLoaderRoute: typeof SignUpImport
      parentRoute: typeof rootRoute
    }
    '/tweet-assistant': {
      id: '/tweet-assistant'
      path: '/tweet-assistant'
      fullPath: '/tweet-assistant'
      preLoaderRoute: typeof TweetAssistantImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/$splat': typeof SplatRoute
  '/404': typeof R404Route
  '/dashboard': typeof DashboardRoute
  '/image-generation': typeof ImageGenerationRoute
  '/live-search': typeof LiveSearchRoute
  '/mentions': typeof MentionsRoute
  '/onboarding': typeof OnboardingRoute
  '/pricing': typeof PricingRoute
  '/sign-in': typeof SignInRoute
  '/sign-up': typeof SignUpRoute
  '/tweet-assistant': typeof TweetAssistantRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/$splat': typeof SplatRoute
  '/404': typeof R404Route
  '/dashboard': typeof DashboardRoute
  '/image-generation': typeof ImageGenerationRoute
  '/live-search': typeof LiveSearchRoute
  '/mentions': typeof MentionsRoute
  '/onboarding': typeof OnboardingRoute
  '/pricing': typeof PricingRoute
  '/sign-in': typeof SignInRoute
  '/sign-up': typeof SignUpRoute
  '/tweet-assistant': typeof TweetAssistantRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/$splat': typeof SplatRoute
  '/404': typeof R404Route
  '/dashboard': typeof DashboardRoute
  '/image-generation': typeof ImageGenerationRoute
  '/live-search': typeof LiveSearchRoute
  '/mentions': typeof MentionsRoute
  '/onboarding': typeof OnboardingRoute
  '/pricing': typeof PricingRoute
  '/sign-in': typeof SignInRoute
  '/sign-up': typeof SignUpRoute
  '/tweet-assistant': typeof TweetAssistantRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/$splat'
    | '/404'
    | '/dashboard'
    | '/image-generation'
    | '/live-search'
    | '/mentions'
    | '/onboarding'
    | '/pricing'
    | '/sign-in'
    | '/sign-up'
    | '/tweet-assistant'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/$splat'
    | '/404'
    | '/dashboard'
    | '/image-generation'
    | '/live-search'
    | '/mentions'
    | '/onboarding'
    | '/pricing'
    | '/sign-in'
    | '/sign-up'
    | '/tweet-assistant'
  id:
    | '__root__'
    | '/'
    | '/$splat'
    | '/404'
    | '/dashboard'
    | '/image-generation'
    | '/live-search'
    | '/mentions'
    | '/onboarding'
    | '/pricing'
    | '/sign-in'
    | '/sign-up'
    | '/tweet-assistant'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  SplatRoute: typeof SplatRoute
  R404Route: typeof R404Route
  DashboardRoute: typeof DashboardRoute
  ImageGenerationRoute: typeof ImageGenerationRoute
  LiveSearchRoute: typeof LiveSearchRoute
  MentionsRoute: typeof MentionsRoute
  OnboardingRoute: typeof OnboardingRoute
  PricingRoute: typeof PricingRoute
  SignInRoute: typeof SignInRoute
  SignUpRoute: typeof SignUpRoute
  TweetAssistantRoute: typeof TweetAssistantRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  SplatRoute: SplatRoute,
  R404Route: R404Route,
  DashboardRoute: DashboardRoute,
  ImageGenerationRoute: ImageGenerationRoute,
  LiveSearchRoute: LiveSearchRoute,
  MentionsRoute: MentionsRoute,
  OnboardingRoute: OnboardingRoute,
  PricingRoute: PricingRoute,
  SignInRoute: SignInRoute,
  SignUpRoute: SignUpRoute,
  TweetAssistantRoute: TweetAssistantRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/$splat",
        "/404",
        "/dashboard",
        "/image-generation",
        "/live-search",
        "/mentions",
        "/onboarding",
        "/pricing",
        "/sign-in",
        "/sign-up",
        "/tweet-assistant"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/$splat": {
      "filePath": "$splat.tsx"
    },
    "/404": {
      "filePath": "404.tsx"
    },
    "/dashboard": {
      "filePath": "dashboard.tsx"
    },
    "/image-generation": {
      "filePath": "image-generation.tsx"
    },
    "/live-search": {
      "filePath": "live-search.tsx"
    },
    "/mentions": {
      "filePath": "mentions.tsx"
    },
    "/onboarding": {
      "filePath": "onboarding.tsx"
    },
    "/pricing": {
      "filePath": "pricing.tsx"
    },
    "/sign-in": {
      "filePath": "sign-in.tsx"
    },
    "/sign-up": {
      "filePath": "sign-up.tsx"
    },
    "/tweet-assistant": {
      "filePath": "tweet-assistant.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
