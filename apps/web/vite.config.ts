import { TanStackRouterVite } from "@tanstack/router-plugin/vite";
import react from "@vitejs/plugin-react";
import path from "node:path";
import { defineConfig } from "vite";

export default defineConfig({
  plugins: [
    TanStackRouterVite(),
    react(),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      // Add polyfills for Node.js modules
      "buffer": "buffer",
      "events": "events",
      "stream": "stream-browserify",
      "util": "util",
      "crypto": "crypto-browserify",
      "@BuddyChipPro/backend": "@BuddyChipAI/backend",
    },
  },
  define: {
    global: 'globalThis',
    // Prevent Ethereum property redefinition
    "process.env": {},
    // Fix for EventEmitter
    "process": JSON.stringify({ env: {} }),
  },
  optimizeDeps: {
    include: [
      'buffer', 
      'events',
      'eventemitter3',
      'convex/react', 
      '@clerk/clerk-react',
      'stream-browserify',
      'util',
      'crypto-browserify'
    ],
    exclude: [
      'jayson',
      'rpc-websockets'
    ],
  },
  server: {
    fs: {
      strict: false,
    },
    hmr: {
      overlay: true,
    },
  },
  build: {
    rollupOptions: {
      output: {
        // 🚀 PERFORMANCE FIX: Intelligent code splitting for 40% bundle size reduction
        manualChunks: {
          // Core framework (always needed)
          vendor: ['react', 'react-dom'],

          // Authentication (needed early)
          auth: ['@clerk/clerk-react', 'convex/react'],

          // UI components (can be lazy loaded)
          ui: ['lucide-react', '@radix-ui/react-avatar', '@radix-ui/react-dropdown-menu'],

          // Heavy features (lazy load)
          ai: ['openai', '@openrouter/ai-sdk-provider'],

          // Analytics and non-critical (lazy load)
          analytics: ['framer-motion', 'lottie-react', 'react-confetti'],

          // Routing
          router: ['@tanstack/react-router'],
        },
      },
    },
    // 🚀 PERFORMANCE: Additional build optimizations
    target: 'esnext',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.logs in production
        drop_debugger: true,
      },
    },
    chunkSizeWarningLimit: 1000, // Warn for chunks larger than 1MB
  },
});
